import { loadEnvironment } from "./config/env-loader";

// Load environment first before importing constants
loadEnvironment();

import bot from "./bot";
import { HealthcheckService } from "./services/healthcheck";
import { expressHttpServer } from "./services/express-server";
import { NODE_ENV, PORT, WEBHOOK_URL, WEB_APP_URL } from "./app.constants";
import { log } from "./utils/logger";
import { IndexLogger } from "./index.logger";
import { botMessages } from "./intl/messages";
import { APP_NAME } from "./constants/bot-commands";

IndexLogger.logStartupConfiguration({
  NODE_ENV,
  PORT,
  WEBHOOK_URL,
  processId: process.pid,
});

async function startBot() {
  try {
    IndexLogger.logBotStarting();

    HealthcheckService.setBotStartTime();

    await expressHttpServer.start();

    (WEBHOOK_URL
      ? bot.launch({
          webhook: {
            domain: WEBHOOK_URL,
            path: "/webhook",
          },
        })
      : bot.launch()
    )
      .then(() => {
        expressHttpServer.setReady(true);
      })
      .catch((error: any) => {
        IndexLogger.logBotLaunchFailed({
          error,
        });
        if (
          error.message?.includes("409") ||
          error.message?.includes("Conflict")
        ) {
          IndexLogger.logBotConflictWarning();
        }
      });

    try {
      // Validate WEB_APP_URL before setting menu button
      log.info("Validating WEB_APP_URL", {
        operation: "bot_configuration",
        webAppUrl: WEB_APP_URL,
      });

      if (!WEB_APP_URL?.startsWith("http")) {
        throw new Error(
          `Invalid WEB_APP_URL: ${WEB_APP_URL}. Must be a valid HTTP/HTTPS URL.`
        );
      }

      await bot.telegram.setChatMenuButton({
        menuButton: {
          type: "web_app",
          text: APP_NAME,
          web_app: {
            url: WEB_APP_URL,
          },
        },
      });

      IndexLogger.logMenuButtonConfigured();

      const menuButton = await bot.telegram.getChatMenuButton();
      IndexLogger.logMenuButtonVerification({
        menuButtonType: menuButton.type,
        menuButtonText: (menuButton as any).text,
        webAppUrl: (menuButton as any).web_app?.url?.substring(0, 50) + "...",
      });
    } catch (error) {
      IndexLogger.logMenuButtonSetFailed({
        error,
        webAppUrl: WEB_APP_URL,
      });
    }

    try {
      const { TNoContext } = await import("./i18n");
      const { botMessages } = await import("./intl/messages");

      await bot.telegram.setMyCommands([
        {
          command: "start",
          description: TNoContext(botMessages.startCommandDescription.id),
        },
        {
          command: "help",
          description: TNoContext(botMessages.helpCommandDescription.id),
        },
        {
          command: "health",
          description: TNoContext(botMessages.healthCommandDescription.id),
        },
      ]);
      IndexLogger.logCommandsConfigured();
    } catch (error) {
      IndexLogger.logCommandsSetFailed({
        error,
      });
    }

    try {
      const description = botMessages.botDescription.defaultMessage.replace(
        /{APP_NAME}/g,
        APP_NAME
      );
      const shortDescription =
        botMessages.botShortDescription.defaultMessage.replace(
          /{APP_NAME}/g,
          APP_NAME
        );

      await bot.telegram.setMyDescription(description);
      await bot.telegram.setMyShortDescription(shortDescription);

      IndexLogger.logBotDescriptionConfigured({
        description,
        shortDescription,
      });
    } catch (error) {
      IndexLogger.logBotDescriptionSetFailed({
        error,
      });
    }

    IndexLogger.logBotSetupCompleted();
  } catch (error) {
    IndexLogger.logBotStartFailed({
      error,
    });
    process.exit(1);
  }
}

async function gracefulShutdown(signal: string) {
  IndexLogger.logGracefulShutdownStarted({
    signal,
  });

  const forceExitTimeout = setTimeout(() => {
    IndexLogger.logGracefulShutdownTimeout({
      signal,
    });
    process.exit(1);
  }, 8000); // 8 seconds, leaving 2 seconds buffer before Cloud Run SIGKILL

  try {
    IndexLogger.logBotStopping();
    bot.stop(signal);

    IndexLogger.logHttpServerStopping();
    await expressHttpServer.stop();

    IndexLogger.logWebhookCleanupSkipped();

    clearTimeout(forceExitTimeout);
    IndexLogger.logGracefulShutdownCompleted();
    process.exit(0);
  } catch (error) {
    clearTimeout(forceExitTimeout);
    IndexLogger.logShutdownError({
      error,
    });
    process.exit(1);
  }
}

process.once("SIGINT", () => gracefulShutdown("SIGINT"));
process.once("SIGTERM", () => gracefulShutdown("SIGTERM"));

startBot();
