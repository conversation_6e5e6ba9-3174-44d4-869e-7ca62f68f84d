{"businessConnection.giftReadyForBuyer": "🎁 Great news! Your gift for order #{orderNumber} is ready for delivery. Please check your orders.", "businessConnection.giftSentError": "❌ Failed to send gift to relayer. Please try again.", "businessConnection.giftSentSuccess": "✅ Gift successfully sent to relayer! The buyer will be notified.", "businessConnection.giftTransferGenericError": "❌ Failed to transfer gift. Please try again.", "businessConnection.giftTransferredSuccess": "✅ Gift successfully transferred!", "businessConnection.incorrectGift": "❌ This gift does not match the order requirements. Please send the correct gift.", "businessConnection.noGiftToTransfer": "❌ No gift found to transfer.", "businessConnection.orderNotFound": "❌ Order not found. Please check the order ID and try again.", "businessConnection.processingGift": "⏳ Processing your gift...", "businessConnection.processingWithdrawal": "⏳ Processing your withdrawal...", "businessConnection.withdrawalError": "❌ Failed to withdraw gift. Please try again.", "businessConnection.withdrawalSuccess": "✅ Gift successfully withdrawn!", "buttons.backToOrders": "🔙 Back to Orders", "buttons.buyOrders": "🛒 Buy Orders", "buttons.cancel": "❌ Cancel", "buttons.contactSupport": "📞 Contact Support", "buttons.depositGift": "📦 Deposit Gift", "buttons.myGifts": "🎁 My Gifts", "buttons.openMarketplace": "🌐 Open Marketplace", "buttons.openMarketplaceButton": "🌐 Open Marketplace", "buttons.orderHelpButton": "📋 Order Help", "buttons.readyToSendGift": "🎁 I'm ready to send gift", "buttons.sellOrders": "💰 Sell Orders", "buttons.viewAllOrders": "📋 View All Orders", "buttons.viewMyOrders": "👤 View My Orders", "buttons.withdrawGift": "Withdraw {giftName} {orderInfo}", "callbacks.backToMenu": "🏠 Back to Main Menu", "callbacks.buyOrdersTitle": "🛒 Your Buy Orders ({count} total)", "commands.health.description": "Check bot health status", "commands.health.error": "❌ Error checking health status", "commands.health.lastCheck": "{status}\n\nLast healthcheck: {timestamp}", "commands.health.noData": "❌ No healthcheck data found", "commands.health.statusHealthy": "✅ Healthy", "commands.health.statusUnhealthy": "⚠️ Unhealthy", "commands.help.description": "Show help information", "commands.start.description": "Start the bot and show main menu", "common.botDescription": "Welcome to {APP_NAME}🎁 – the first liquid pre-market for Telegram unupgraded gifts!\nWith {APP_NAME}, you can:\n\nAs buyer:\n\n🔓 Buy any unupgraded TG gift.\n💸 Resell for instant profit\n\nAs seller:\n\n🎁 Sell unupgraded TG gift with just 50% collateral.\n💰 Earn fees from resales\n\nEnjoy fast, safe, and easy gift trading!", "common.botGenericError": "Sorry, something went wrong. Please try again later.", "common.botShortDescription": "🎁 {APP_NAME} - Telegram Gifts Marketplace", "common.genericError": "❌ Failed to process your request. Please try again later.", "common.help": "Welcome to {APP_NAME}! \n \n{PREM_CHANNEL} - Community \n{PREM_SUPPORT_OFFICIAL} - Support \n{relayerUsername} - Gift Relayer", "common.telegramIdError": "❌ Unable to identify your Telegram ID. Please try again.", "common.welcome": "🛍️ Welcome to the {APP_NAME} Bot!", "common.withdrawalErrorFallback": "Failed to withdraw gift", "common.giftWithdrawalInstructions": "✅ Gift selected for withdrawal! Now go to @{relayerUsername} and send the command 'get a gift' to complete the withdrawal.", "common.giftSelectedForWithdrawal": "Gift selected for withdrawal", "common.giftWithdrawalSimulationSuccess": "🎉 Simulation Mode: Gift withdrawn successfully!", "common.giftWithdrawalSimulationFailed": "❌ Simulation Mode: Gift withdrawal failed", "common.giftWithdrawalSimulatedSuccessfully": "Gift withdrawal simulated successfully", "common.giftWithdrawalSimulationFailedCallback": "Gift withdrawal simulation failed", "gifts.depositSuccess": "🎁 Gift deposited successfully! You can now see your gift in the {APP_NAME} app under 'My Gifts' tab.", "gifts.fetchError": "❌ Error fetching your gifts. Please try again later.", "gifts.fetchingGifts": "🔄 Fetching your gifts...", "gifts.noGiftsAvailable": "📭 No gifts available. Send gifts to {relayerUsername} to get started!", "gifts.noLinkedOrder": "📦 No linked order", "gifts.unknownGift": "Unknown Gift", "language.detectionPromptRussian": "🌍 We detected that you are using the app in Russian language. Would you like to switch to Russian?", "language.detectionPromptUkrainian": "🌍 We detected that you are using the app in Ukrainian language. Would you like to switch to Ukrainian?", "language.keepEnglish": "No, I want to stay in English", "language.setToEnglish": "✅ Language set to English. Welcome to {APP_NAME} Bot!", "language.setToRussian": "✅ Язык изменен на русский. Добро пожаловать в {APP_NAME} Bot!", "language.setToUkrainian": "✅ Мову змінено на українську. Ласкаво просимо до {APP_NAME} Bot!", "language.switchToRussian": "Yes, let's switch to Russian", "language.switchToUkrainian": "Yes, let's switch to Ukrainian", "simulation.giftDepositError": "❌ Failed to deposit mock gift: {errorMessage}\n\nPlease try again or contact support if the issue persists.", "simulation.giftDepositMode": "🎁 Deposit a Gift (Simulation Mode)\n\n🔧 SIMULATION MODE: Generating and depositing a mock gift...", "simulation.giftDepositSuccess": "🎁 Gift Details:\n• Name: {giftName}\n• Model: {modelName}\n• Symbol: {symbolName}\n• Backdrop: {backdropName}\n\nYour gift is now available in the {APP_NAME} app under 'My Gifts' tab.", "simulation.giftWithdrawalError": "❌ Gift withdrawal failed in simulation mode: {errorMessage}\n\nPlease try again or contact support if the issue persists.", "simulation.giftWithdrawalMode": "🎁 Gift Withdrawal (Simulation Mode)\n\n🔧 SIMULATION MODE: Processing gift withdrawal...", "simulation.giftWithdrawalSuccess": "✅ Gift withdrawal completed successfully in simulation mode!\n\n🔧 SIMULATION MODE: Gift transfer skipped. In real mode, your gift would be transferred to you now.", "simulation.orderActivation": "🔧 DEV MODE: This is a simulation. In production, you would need to deposit a gift first.", "simulation.orderView": "🔧 DEV MODE: This order is in simulation mode.", "simulation.sellerGiftDeposit": "🔧 DEV MODE: This is a simulation. In production, you would send the gift to @premrelayer.", "support.contactInfo": "📞 For support, please contact {PREM_SUPPORT_OFFICIAL}"}