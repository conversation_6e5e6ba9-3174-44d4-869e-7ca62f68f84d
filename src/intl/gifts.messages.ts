import { defineMessages } from "@formatjs/intl";

export const giftsMessages = defineMessages({
  fetchingGifts: {
    id: "gifts.fetchingGifts",
    defaultMessage: "🔄 Fetching your gifts...",
  },
  noGiftsAvailable: {
    id: "gifts.noGiftsAvailable",
    defaultMessage:
      "📭 No gifts available. Send gifts to {relayerUsername} to get started!",
  },
  depositSuccess: {
    id: "gifts.depositSuccess",
    defaultMessage:
      "🎁 Gift deposited successfully! You can now see your gift in the {APP_NAME} app under 'My Gifts' tab.",
  },
  unknownGift: {
    id: "gifts.unknownGift",
    defaultMessage: "Unknown Gift",
  },
  noLinkedOrder: {
    id: "gifts.noLinkedOrder",
    defaultMessage: "\n📦 No linked order",
  },
  linkedOrder: {
    id: "gifts.linkedOrder",
    defaultMessage: "\n📦 Order #{orderNumber} ({orderStatus})",
  },
  withdrawInstructions: {
    id: "gifts.withdrawInstructions",
    defaultMessage:
      "Use the buttons below to withdraw specific gifts or manage your orders.",
  },
  fetchError: {
    id: "gifts.fetchError",
    defaultMessage: "❌ Error fetching your gifts. Please try again later.",
  },
});
